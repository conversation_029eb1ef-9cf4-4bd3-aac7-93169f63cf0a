from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from api.serializers.auth_serializers import LoginSerializer, UserProfileSerializer, TokenResponseSerializer
from account.models.user_model import UserModel


@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    """
    Endpoint de connexion personnalisé
    POST /api/v1/auth/login/
    """
    serializer = LoginSerializer(data=request.data)
    
    if not serializer.is_valid():
        return Response({
            'success': False,
            'message': 'Données invalides',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    email = serializer.validated_data['email']
    password = serializer.validated_data['password']
    
    try:
        # Vérifier si l'utilisateur existe
        user = UserModel.objects.get(email=email)
        
        # Authentifier l'utilisateur
        if user.check_password(password):
            if not user.is_active:
                return Response({
                    'success': False,
                    'message': 'Compte désactivé'
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # Générer les tokens JWT
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            # Sérialiser les données utilisateur
            user_serializer = UserProfileSerializer(user)
            
            # Récupérer les autorités
            authorities = list(
                user.user_authorities.filter(status=True)
                .values_list('authority__name', flat=True)
            )
            
            return Response({
                'success': True,
                'access': str(access_token),
                'refresh': str(refresh),
                'user': user_serializer.data,
                'authorities': authorities,
                'expires_in': 86400  # 24 heures en secondes
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': 'Email ou mot de passe incorrect'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
    except UserModel.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Email ou mot de passe incorrect'
        }, status=status.HTTP_401_UNAUTHORIZED)
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Erreur serveur lors de la connexion'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def logout_view(request):
    """
    Endpoint de déconnexion
    POST /api/v1/auth/logout/
    """
    try:
        refresh_token = request.data.get('refresh_token')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()
        
        return Response({
            'success': True,
            'message': 'Déconnexion réussie'
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'success': True,
            'message': 'Déconnexion réussie'
        }, status=status.HTTP_200_OK)


@api_view(['GET'])
def profile_view(request):
    """
    Endpoint pour récupérer le profil utilisateur
    GET /api/v1/auth/profile/
    """
    if not request.user.is_authenticated:
        return Response({
            'success': False,
            'message': 'Non authentifié'
        }, status=status.HTTP_401_UNAUTHORIZED)
    
    user_serializer = UserProfileSerializer(request.user)
    authorities = list(
        request.user.user_authorities.filter(status=True)
        .values_list('authority__name', flat=True)
    )
    
    return Response({
        'success': True,
        'user': user_serializer.data,
        'authorities': authorities
    }, status=status.HTTP_200_OK)


@api_view(['PUT'])
def update_profile_view(request):
    """
    Endpoint pour mettre à jour le profil utilisateur
    PUT /api/v1/auth/profile/
    """
    if not request.user.is_authenticated:
        return Response({
            'success': False,
            'message': 'Non authentifié'
        }, status=status.HTTP_401_UNAUTHORIZED)
    
    serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
    
    if serializer.is_valid():
        serializer.save()
        return Response({
            'success': True,
            'user': serializer.data,
            'message': 'Profil mis à jour avec succès'
        }, status=status.HTTP_200_OK)
    else:
        return Response({
            'success': False,
            'message': 'Données invalides',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
