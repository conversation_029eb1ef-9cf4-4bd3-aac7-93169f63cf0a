import React from 'react';
import {
  EyeIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  CheckCircleIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';

const DetectionStats = ({ stats }) => {
  const statItems = [
    {
      label: 'Total',
      value: stats.total,
      icon: EyeIcon,
      color: 'text-night-blue',
      bgColor: 'bg-gray-50',
      description: 'Détections totales'
    },
    {
      label: 'Actives',
      value: stats.active,
      icon: ExclamationTriangleIcon,
      color: 'text-alert-red',
      bgColor: 'bg-red-50',
      description: 'Nécessitent une action'
    },
    {
      label: 'En cours',
      value: stats.investigating,
      icon: ClockIcon,
      color: 'text-gold',
      bgColor: 'bg-yellow-50',
      description: 'En cours d\'investigation'
    },
    {
      label: 'Résolues',
      value: stats.resolved,
      icon: CheckCircleIcon,
      color: 'text-forest-green',
      bgColor: 'bg-green-50',
      description: 'Traitées avec succès'
    },
    {
      label: 'Priorité élevée',
      value: stats.highPriority,
      icon: ChartBarIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Détections prioritaires'
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
      {statItems.map((item, index) => {
        const Icon = item.icon;
        return (
          <div 
            key={index}
            className={`${item.bgColor} rounded-lg p-4 text-center hover:shadow-sm transition-shadow`}
          >
            <div className="flex items-center justify-center mb-2">
              <Icon className={`h-6 w-6 ${item.color}`} />
            </div>
            <div className={`text-2xl font-bold ${item.color}`}>
              {item.value}
            </div>
            <div className="text-sm text-gray-600 font-medium">
              {item.label}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {item.description}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DetectionStats;
