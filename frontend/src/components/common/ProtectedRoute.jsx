import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const ProtectedRoute = ({ children, requiredPermission = null, allowedRoles = null }) => {
  const { isAuthenticated, user, loading, hasPermission, getPrimaryRole } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-night-blue">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    // Rediriger vers la page de connexion avec l'URL de retour
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Vérifier les permissions spécifiques
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="bg-white p-8 rounded-lg shadow-lg max-w-md">
            <div className="text-alert-red text-6xl mb-4">🚫</div>
            <h2 className="text-2xl font-title font-bold text-night-blue mb-4">
              Accès refusé
            </h2>
            <p className="text-gray-600 mb-6">
              Vous n'avez pas les permissions nécessaires pour accéder à cette page.
            </p>
            <button
              onClick={() => window.history.back()}
              className="btn-secondary"
            >
              Retour
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Vérifier les rôles autorisés
  if (allowedRoles && allowedRoles.length > 0) {
    const userRole = getPrimaryRole();
    if (!allowedRoles.includes(userRole)) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="bg-white p-8 rounded-lg shadow-lg max-w-md">
              <div className="text-alert-red text-6xl mb-4">🚫</div>
              <h2 className="text-2xl font-title font-bold text-night-blue mb-4">
                Accès refusé
              </h2>
              <p className="text-gray-600 mb-6">
                Cette page est réservée aux utilisateurs avec le rôle : {allowedRoles.join(', ')}
              </p>
              <button
                onClick={() => window.history.back()}
                className="btn-secondary"
              >
                Retour
              </button>
            </div>
          </div>
        </div>
      );
    }
  }

  return children;
};

export default ProtectedRoute;
