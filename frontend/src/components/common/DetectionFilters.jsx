import React from 'react';
import {
  MagnifyingGlassIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';

const DetectionFilters = ({ 
  filters, 
  onFiltersChange, 
  sortBy, 
  setSortBy, 
  sortOrder, 
  setSortOrder 
}) => {
  const handleFilterChange = (key, value) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        {/* Recherche */}
        <div className="lg:col-span-2 relative">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher par titre ou localisation..."
            className="form-input pl-10"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
          />
        </div>

        {/* Filtre par statut */}
        <select
          className="form-input"
          value={filters.status}
          onChange={(e) => handleFilterChange('status', e.target.value)}
        >
          <option value="all">Tous les statuts</option>
          <option value="active">Actif</option>
          <option value="investigating">En cours</option>
          <option value="resolved">Résolu</option>
        </select>

        {/* Filtre par priorité */}
        <select
          className="form-input"
          value={filters.priority}
          onChange={(e) => handleFilterChange('priority', e.target.value)}
        >
          <option value="all">Toutes priorités</option>
          <option value="high">Élevée</option>
          <option value="medium">Moyenne</option>
          <option value="low">Faible</option>
        </select>

        {/* Filtre par type */}
        <select
          className="form-input"
          value={filters.type}
          onChange={(e) => handleFilterChange('type', e.target.value)}
        >
          <option value="all">Tous les types</option>
          <option value="mining">Exploitation minière</option>
          <option value="deforestation">Déforestation</option>
          <option value="vegetation">Végétation</option>
          <option value="pollution">Pollution</option>
        </select>

        {/* Tri */}
        <div className="flex space-x-2">
          <select
            className="form-input flex-1"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="date">Date</option>
            <option value="confidence">Confiance</option>
            <option value="priority">Priorité</option>
            <option value="title">Titre</option>
          </select>
          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            title={`Tri ${sortOrder === 'asc' ? 'croissant' : 'décroissant'}`}
          >
            {sortOrder === 'asc' ? 
              <ArrowUpIcon className="h-4 w-4" /> : 
              <ArrowDownIcon className="h-4 w-4" />
            }
          </button>
        </div>
      </div>

      {/* Filtres avancés (optionnel) */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Filtre par région */}
          <select
            className="form-input"
            value={filters.region || 'all'}
            onChange={(e) => handleFilterChange('region', e.target.value)}
          >
            <option value="all">Toutes les régions</option>
            <option value="est">Région Est</option>
            <option value="ouest">Région Ouest</option>
            <option value="nord">Région Nord</option>
            <option value="sud">Région Sud</option>
            <option value="centre">Région Centre</option>
          </select>

          {/* Filtre par période */}
          <select
            className="form-input"
            value={filters.dateRange || '7days'}
            onChange={(e) => handleFilterChange('dateRange', e.target.value)}
          >
            <option value="today">Aujourd'hui</option>
            <option value="7days">7 derniers jours</option>
            <option value="30days">30 derniers jours</option>
            <option value="90days">90 derniers jours</option>
            <option value="6months">6 derniers mois</option>
            <option value="1year">1 an</option>
          </select>

          {/* Filtre par niveau de confiance */}
          <select
            className="form-input"
            value={filters.confidence || 'all'}
            onChange={(e) => handleFilterChange('confidence', e.target.value)}
          >
            <option value="all">Tous niveaux</option>
            <option value="high">Élevé (≥ 90%)</option>
            <option value="medium">Moyen (70-89%)</option>
            <option value="low">Faible (< 70%)</option>
          </select>
        </div>
      </div>

      {/* Résumé des filtres actifs */}
      {(filters.search || filters.status !== 'all' || filters.priority !== 'all' || 
        filters.type !== 'all' || (filters.region && filters.region !== 'all') ||
        (filters.confidence && filters.confidence !== 'all')) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-2">
              {filters.search && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Recherche: "{filters.search}"
                </span>
              )}
              {filters.status !== 'all' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Statut: {filters.status}
                </span>
              )}
              {filters.priority !== 'all' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Priorité: {filters.priority}
                </span>
              )}
              {filters.type !== 'all' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Type: {filters.type}
                </span>
              )}
            </div>
            <button
              onClick={() => onFiltersChange({
                status: 'all',
                region: 'all',
                dateRange: '7days',
                search: '',
                priority: 'all',
                type: 'all',
                confidence: 'all'
              })}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Réinitialiser
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DetectionFilters;
