import React from 'react';
import {
  MapPinIcon,
  CalendarIcon,
  PhotoIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

const DetectionCard = ({ detection, viewMode = 'grid', onViewDetails, onAnalyze }) => {
  const getStatusBadge = (status) => {
    const StatusIcon = getStatusIcon(status);
    const statusConfig = {
      active: { class: 'bg-red-50 text-alert-red border-alert-red', text: 'Actif' },
      investigating: { class: 'bg-yellow-50 text-gold border-gold', text: 'En cours' },
      resolved: { class: 'bg-green-50 text-forest-green border-forest-green', text: 'Résolu' },
    };
    const config = statusConfig[status] || statusConfig.active;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.class}`}>
        <StatusIcon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    );
  };

  const getStatusIcon = (status) => {
    const icons = {
      active: ExclamationTriangleIcon,
      investigating: ClockIcon,
      resolved: CheckCircleIcon,
    };
    return icons[status] || ClockIcon;
  };

  const getPriorityBadge = (priority) => {
    const colors = {
      high: 'bg-alert-red text-white',
      medium: 'bg-gold text-night-blue',
      low: 'bg-forest-green text-white',
    };
    const labels = {
      high: 'Élevée',
      medium: 'Moyenne',
      low: 'Faible',
    };
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${colors[priority]}`}>
        {labels[priority]}
      </span>
    );
  };

  const getTypeColor = (type) => {
    const colors = {
      mining: 'bg-night-blue text-white',
      deforestation: 'bg-forest-green text-white',
      vegetation: 'bg-green-600 text-white',
      pollution: 'bg-alert-red text-white',
    };
    return colors[type] || 'bg-gray-500 text-white';
  };

  const getTypeLabel = (type) => {
    const labels = {
      mining: 'Exploitation',
      deforestation: 'Déforestation',
      vegetation: 'Végétation',
      pollution: 'Pollution',
    };
    return labels[type] || type;
  };

  if (viewMode === 'grid') {
    return (
      <div className="bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow overflow-hidden">
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${getTypeColor(detection.type)}`}>
                  {getTypeLabel(detection.type)}
                </span>
                {getPriorityBadge(detection.priority)}
              </div>
              <h3 className="text-lg font-semibold text-night-blue mb-2">
                {detection.title}
              </h3>
            </div>
            {getStatusBadge(detection.status)}
          </div>

          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {detection.description}
          </p>

          <div className="space-y-2 text-sm text-gray-500">
            <div className="flex items-center">
              <MapPinIcon className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">{detection.location}</span>
            </div>
            <div className="flex items-center">
              <CalendarIcon className="h-4 w-4 mr-2 flex-shrink-0" />
              <span>{new Date(detection.date).toLocaleDateString('fr-FR')}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <PhotoIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>{detection.images} image(s)</span>
              </div>
              <div className="flex items-center">
                <ChartBarIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>{detection.confidence}%</span>
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex space-x-2">
            <button 
              onClick={() => onViewDetails?.(detection)}
              className="btn-outline text-sm flex-1"
            >
              Voir détails
            </button>
            <button 
              onClick={() => onAnalyze?.(detection)}
              className="btn-secondary text-sm flex-1"
            >
              Analyser
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Vue en liste
  return (
    <div className="bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h3 className="text-lg font-semibold text-night-blue">
              {detection.title}
            </h3>
            {getStatusBadge(detection.status)}
            {getPriorityBadge(detection.priority)}
            <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${getTypeColor(detection.type)}`}>
              {getTypeLabel(detection.type)}
            </span>
          </div>

          <p className="text-gray-600 mb-3">{detection.description}</p>

          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center">
              <MapPinIcon className="h-4 w-4 mr-1" />
              {detection.location}
            </div>
            <div className="flex items-center">
              <CalendarIcon className="h-4 w-4 mr-1" />
              {new Date(detection.date).toLocaleDateString('fr-FR')}
            </div>
            <div className="flex items-center">
              <ChartBarIcon className="h-4 w-4 mr-1" />
              Confiance: {detection.confidence}%
            </div>
            <div className="flex items-center">
              <PhotoIcon className="h-4 w-4 mr-1" />
              {detection.images} image(s)
            </div>
            {detection.area && (
              <div>Zone: {detection.area}</div>
            )}
            {detection.source && (
              <div>Source: {detection.source}</div>
            )}
            {detection.analyst && (
              <div>Analyste: {detection.analyst}</div>
            )}
          </div>
        </div>

        <div className="flex space-x-2 ml-4">
          <button 
            onClick={() => onViewDetails?.(detection)}
            className="btn-outline text-sm"
          >
            Voir détails
          </button>
          <button 
            onClick={() => onAnalyze?.(detection)}
            className="btn-secondary text-sm"
          >
            Analyser
          </button>
        </div>
      </div>
    </div>
  );
};

export default DetectionCard;
