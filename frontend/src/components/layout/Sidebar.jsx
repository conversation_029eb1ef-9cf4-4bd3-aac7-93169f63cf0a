import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  HomeIcon,
  ChartBarIcon,
  MapIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  CogIcon,
  UserGroupIcon,
  EyeIcon,
  BeakerIcon,
  ClipboardDocumentListIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

const Sidebar = ({ isOpen, setIsOpen }) => {
  const { user, hasPermission, getPrimaryRole } = useAuth();
  const location = useLocation();

  const menuItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon,
      roles: ['Administrateur', 'Responsable Régional', 'Agent Analyste', 'Agent Technique', 'Agent Terrain'],
    },
    {
      name: 'Détections',
      href: '/detections',
      icon: EyeIcon,
      roles: ['Administrateur', 'Responsable Régional', 'Agent Analyste', 'Agent Technique'],
    },
    {
      name: 'Analy<PERSON>',
      href: '/analyses',
      icon: BeakerIcon,
      permission: 'can_launch_analysis',
    },
    {
      name: 'Cartes',
      href: '/maps',
      icon: MapIcon,
      roles: ['Administrateur', 'Responsable Régional', 'Agent Analyste', 'Agent Technique'],
    },
    {
      name: 'Alertes',
      href: '/alerts',
      icon: ExclamationTriangleIcon,
      roles: ['Administrateur', 'Responsable Régional', 'Agent Analyste', 'Agent Technique', 'Agent Terrain'],
    },
    {
      name: 'Investigations',
      href: '/investigations',
      icon: ClipboardDocumentListIcon,
      permission: 'can_manage_investigations',
    },
    {
      name: 'Rapports',
      href: '/reports',
      icon: DocumentTextIcon,
      permission: 'can_view_reports',
    },
    {
      name: 'Statistiques',
      href: '/statistics',
      icon: ChartBarIcon,
      permission: 'can_view_stats',
    },
    {
      name: 'Utilisateurs',
      href: '/users',
      icon: UserGroupIcon,
      permission: 'can_manage_users',
    },
    {
      name: 'Logs',
      href: '/logs',
      icon: CogIcon,
      permission: 'can_view_logs',
    },
  ];

  const isItemVisible = (item) => {
    // Vérifier les permissions
    if (item.permission && !hasPermission(item.permission)) {
      return false;
    }

    // Vérifier les rôles
    if (item.roles) {
      const userRole = getPrimaryRole();
      return item.roles.includes(userRole);
    }

    return true;
  };

  const isActive = (href) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  return (
    <>
      {/* Overlay pour mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`sidebar ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-night-blue-light">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gold rounded-full flex items-center justify-center">
              <span className="text-night-blue font-bold text-sm">GS</span>
            </div>
            <div>
              <h1 className="text-lg font-title font-bold text-gold">GoldSentinel</h1>
              <p className="text-xs text-gray-400">Surveillance Minière</p>
            </div>
          </div>
          
          {/* Bouton fermer pour mobile */}
          <button
            onClick={() => setIsOpen(false)}
            className="lg:hidden text-gray-400 hover:text-white"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-4 border-b border-night-blue-light">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gold rounded-full flex items-center justify-center">
              <span className="text-night-blue font-semibold">
                {user?.first_name?.[0]}{user?.last_name?.[0]}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {user?.full_name}
              </p>
              <p className="text-xs text-gray-400 truncate">
                {getPrimaryRole()}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user?.authorized_region}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-2 py-4 space-y-1">
          {menuItems.filter(isItemVisible).map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`sidebar-item ${
                  isActive(item.href) ? 'sidebar-item-active' : ''
                }`}
                onClick={() => setIsOpen(false)} // Fermer sur mobile après clic
              >
                <Icon className="h-5 w-5 mr-3 flex-shrink-0" />
                <span className="truncate">{item.name}</span>
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-night-blue-light">
          <div className="text-xs text-gray-500 text-center">
            <p>GoldSentinel v3.0</p>
            <p>© 2024 - Surveillance Minière</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
