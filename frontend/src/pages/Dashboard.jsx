import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  ChartBarIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  MapIcon,
  DocumentTextIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const { user, getPrimaryRole, hasPermission } = useAuth();
  const [stats, setStats] = useState({
    totalDetections: 0,
    activeAlerts: 0,
    completedAnalyses: 0,
    pendingInvestigations: 0,
    regionCoverage: 0,
    lastUpdate: new Date(),
  });

  const userRole = getPrimaryRole();

  // Simuler le chargement des statistiques
  useEffect(() => {
    // Ici on ferait un appel API réel
    setStats({
      totalDetections: 1247,
      activeAlerts: 23,
      completedAnalyses: 89,
      pendingInvestigations: 12,
      regionCoverage: 85.6,
      lastUpdate: new Date(),
    });
  }, []);

  // Cartes de statistiques selon le rôle
  const getStatsCards = () => {
    const baseCards = [
      {
        title: 'Détections Totales',
        value: stats.totalDetections.toLocaleString(),
        icon: EyeIcon,
        color: 'bg-blue-500',
        change: '+12%',
        changeType: 'increase',
      },
      {
        title: 'Alertes Actives',
        value: stats.activeAlerts,
        icon: ExclamationTriangleIcon,
        color: 'bg-alert-red',
        change: '-5%',
        changeType: 'decrease',
      },
    ];

    if (hasPermission('can_launch_analysis')) {
      baseCards.push({
        title: 'Analyses Terminées',
        value: stats.completedAnalyses,
        icon: ChartBarIcon,
        color: 'bg-forest-green',
        change: '+8%',
        changeType: 'increase',
      });
    }

    if (hasPermission('can_manage_investigations')) {
      baseCards.push({
        title: 'Investigations en Cours',
        value: stats.pendingInvestigations,
        icon: DocumentTextIcon,
        color: 'bg-gold',
        change: '+3',
        changeType: 'increase',
      });
    }

    return baseCards;
  };

  const StatCard = ({ title, value, icon: Icon, color, change, changeType }) => (
    <div className="card">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-night-blue">{value}</p>
        </div>
        <div className="ml-4">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            changeType === 'increase' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {change}
          </span>
        </div>
      </div>
    </div>
  );

  const QuickAction = ({ title, description, icon: Icon, onClick, color = 'btn-primary' }) => (
    <button
      onClick={onClick}
      className={`${color} w-full text-left p-4 rounded-lg hover:shadow-lg transition-all duration-200`}
    >
      <div className="flex items-center">
        <Icon className="h-8 w-8 mr-4" />
        <div>
          <h3 className="font-semibold">{title}</h3>
          <p className="text-sm opacity-90">{description}</p>
        </div>
      </div>
    </button>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-title font-bold text-night-blue">
            Tableau de Bord
          </h1>
          <p className="text-gray-600 mt-1">
            Bienvenue, {user?.first_name} - {userRole}
          </p>
          <p className="text-sm text-gray-500">
            Région autorisée : {user?.authorized_region}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Dernière mise à jour</p>
          <p className="text-sm font-medium text-night-blue">
            {stats.lastUpdate.toLocaleTimeString()}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {getStatsCards().map((card, index) => (
          <StatCard key={index} {...card} />
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h2 className="text-xl font-title font-semibold text-night-blue">
              Actions Rapides
            </h2>
          </div>
          <div className="space-y-4">
            {hasPermission('can_launch_analysis') && (
              <QuickAction
                title="Nouvelle Analyse"
                description="Lancer une analyse satellite"
                icon={ChartBarIcon}
                onClick={() => console.log('Nouvelle analyse')}
                color="btn-primary"
              />
            )}
            
            <QuickAction
              title="Voir les Alertes"
              description="Consulter les alertes actives"
              icon={ExclamationTriangleIcon}
              onClick={() => console.log('Voir alertes')}
              color="btn-secondary"
            />
            
            <QuickAction
              title="Carte Interactive"
              description="Visualiser les données géospatiales"
              icon={MapIcon}
              onClick={() => console.log('Carte')}
              color="btn-success"
            />

            {hasPermission('can_view_reports') && (
              <QuickAction
                title="Générer Rapport"
                description="Créer un nouveau rapport"
                icon={DocumentTextIcon}
                onClick={() => console.log('Rapport')}
                color="btn-outline"
              />
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-xl font-title font-semibold text-night-blue">
              Activité Récente
            </h2>
          </div>
          <div className="space-y-4">
            {[
              {
                title: 'Détection automatique',
                description: 'Nouvelle zone d\'activité détectée à Bondoukou',
                time: '5 min',
                type: 'detection',
              },
              {
                title: 'Analyse terminée',
                description: 'Analyse satellite du secteur Nord complétée',
                time: '1h',
                type: 'analysis',
              },
              {
                title: 'Rapport généré',
                description: 'Rapport hebdomadaire disponible',
                time: '2h',
                type: 'report',
              },
              {
                title: 'Alerte résolue',
                description: 'Alerte de déforestation dans la région Est',
                time: '4h',
                type: 'alert',
              },
            ].map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type === 'detection' ? 'bg-blue-500' :
                  activity.type === 'analysis' ? 'bg-forest-green' :
                  activity.type === 'report' ? 'bg-gold' :
                  'bg-alert-red'
                }`} />
                <div className="flex-1">
                  <p className="text-sm font-medium text-night-blue">
                    {activity.title}
                  </p>
                  <p className="text-sm text-gray-600">
                    {activity.description}
                  </p>
                  <p className="text-xs text-gray-400 mt-1 flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    Il y a {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Role-specific content */}
      {userRole === 'Administrateur' && (
        <div className="card">
          <div className="card-header">
            <h2 className="text-xl font-title font-semibold text-night-blue">
              Vue Administrateur
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">156</p>
              <p className="text-sm text-gray-600">Utilisateurs Actifs</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">98.5%</p>
              <p className="text-sm text-gray-600">Disponibilité Système</p>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <p className="text-2xl font-bold text-yellow-600">2.3TB</p>
              <p className="text-sm text-gray-600">Données Stockées</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
