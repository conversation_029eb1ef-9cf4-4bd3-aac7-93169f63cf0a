import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  BeakerIcon,
  PlayIcon,
  PauseIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

const Analyses = () => {
  const { user } = useAuth();
  const [analyses, setAnalyses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showNewAnalysisModal, setShowNewAnalysisModal] = useState(false);

  // Données simulées
  useEffect(() => {
    setTimeout(() => {
      setAnalyses([
        {
          id: 1,
          name: 'Analyse Satellite - Région Est',
          type: 'satellite',
          status: 'completed',
          progress: 100,
          startDate: '2024-01-15T09:00:00Z',
          endDate: '2024-01-15T11:30:00Z',
          region: 'Région Est',
          parameters: {
            resolution: '10m',
            bands: ['RGB', 'NIR'],
            cloudCover: '< 10%',
          },
          results: {
            detections: 12,
            alerts: 3,
            confidence: 94,
          },
        },
        {
          id: 2,
          name: 'Analy<PERSON>u',
          type: 'temporal',
          status: 'running',
          progress: 67,
          startDate: '2024-01-15T14:00:00Z',
          endDate: null,
          region: 'Bondoukou',
          parameters: {
            timeRange: '6 mois',
            algorithm: 'Change Detection',
            threshold: '0.3',
          },
          results: null,
        },
        {
          id: 3,
          name: 'Analyse Spectrale - Zone Nord',
          type: 'spectral',
          status: 'pending',
          progress: 0,
          startDate: null,
          endDate: null,
          region: 'Zone Nord',
          parameters: {
            bands: ['SWIR', 'NIR', 'RED'],
            method: 'Supervised Classification',
            samples: 150,
          },
          results: null,
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-forest-green" />;
      case 'running':
        return <PlayIcon className="h-5 w-5 text-gold" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-alert-red" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status) => {
    const statusMap = {
      completed: 'Terminée',
      running: 'En cours',
      pending: 'En attente',
      failed: 'Échouée',
    };
    return statusMap[status] || 'Inconnu';
  };

  const getTypeText = (type) => {
    const typeMap = {
      satellite: 'Satellite',
      temporal: 'Temporelle',
      spectral: 'Spectrale',
      change: 'Détection de changement',
    };
    return typeMap[type] || type;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading-spinner"></div>
        <span className="ml-2">Chargement des analyses...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-title font-bold text-night-blue">
            Analyses
          </h1>
          <p className="text-gray-600 mt-1">
            Gestion et suivi des analyses automatisées
          </p>
        </div>
        <button 
          className="btn-primary"
          onClick={() => setShowNewAnalysisModal(true)}
        >
          <BeakerIcon className="h-5 w-5 mr-2" />
          Nouvelle analyse
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card text-center">
          <div className="text-2xl font-bold text-night-blue">{analyses.length}</div>
          <div className="text-sm text-gray-600">Total analyses</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-gold">
            {analyses.filter(a => a.status === 'running').length}
          </div>
          <div className="text-sm text-gray-600">En cours</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-forest-green">
            {analyses.filter(a => a.status === 'completed').length}
          </div>
          <div className="text-sm text-gray-600">Terminées</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-gray-400">
            {analyses.filter(a => a.status === 'pending').length}
          </div>
          <div className="text-sm text-gray-600">En attente</div>
        </div>
      </div>

      {/* Analyses List */}
      <div className="space-y-4">
        {analyses.map((analysis) => (
          <div key={analysis.id} className="card">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  {getStatusIcon(analysis.status)}
                  <h3 className="text-lg font-semibold text-night-blue">
                    {analysis.name}
                  </h3>
                  <span className="badge badge-secondary">
                    {getTypeText(analysis.type)}
                  </span>
                  <span className="text-sm text-gray-500">
                    {getStatusText(analysis.status)}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-600 mb-2">
                      <strong>Région:</strong> {analysis.region}
                    </p>
                    {analysis.startDate && (
                      <p className="text-sm text-gray-600 mb-2">
                        <strong>Début:</strong> {new Date(analysis.startDate).toLocaleString('fr-FR')}
                      </p>
                    )}
                    {analysis.endDate && (
                      <p className="text-sm text-gray-600">
                        <strong>Fin:</strong> {new Date(analysis.endDate).toLocaleString('fr-FR')}
                      </p>
                    )}
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-2">
                      <strong>Paramètres:</strong>
                    </p>
                    <ul className="text-sm text-gray-500 space-y-1">
                      {Object.entries(analysis.parameters).map(([key, value]) => (
                        <li key={key}>
                          {key}: {Array.isArray(value) ? value.join(', ') : value}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Progress bar */}
                {analysis.status === 'running' && (
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Progression</span>
                      <span>{analysis.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gold h-2 rounded-full transition-all duration-300"
                        style={{ width: `${analysis.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Results */}
                {analysis.results && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-night-blue mb-2">Résultats</h4>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Détections:</span>
                        <span className="ml-2 font-medium">{analysis.results.detections}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Alertes:</span>
                        <span className="ml-2 font-medium">{analysis.results.alerts}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Confiance:</span>
                        <span className="ml-2 font-medium">{analysis.results.confidence}%</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex space-x-2 ml-4">
                {analysis.status === 'completed' && (
                  <button className="btn-success text-sm">
                    Voir résultats
                  </button>
                )}
                {analysis.status === 'running' && (
                  <button className="btn-secondary text-sm">
                    <PauseIcon className="h-4 w-4 mr-1" />
                    Pause
                  </button>
                )}
                {analysis.status === 'pending' && (
                  <button className="btn-primary text-sm">
                    <PlayIcon className="h-4 w-4 mr-1" />
                    Démarrer
                  </button>
                )}
                <button className="btn-outline text-sm">
                  Détails
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* New Analysis Modal */}
      {showNewAnalysisModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-night-blue mb-4">
              Nouvelle Analyse
            </h3>
            <div className="space-y-4">
              <div>
                <label className="form-label">Type d'analyse</label>
                <select className="form-input">
                  <option>Analyse satellite</option>
                  <option>Analyse temporelle</option>
                  <option>Analyse spectrale</option>
                  <option>Détection de changement</option>
                </select>
              </div>
              <div>
                <label className="form-label">Région</label>
                <select className="form-input">
                  <option>Région Est</option>
                  <option>Région Ouest</option>
                  <option>Région Nord</option>
                  <option>Zone personnalisée</option>
                </select>
              </div>
              <div>
                <label className="form-label">Nom de l'analyse</label>
                <input type="text" className="form-input" placeholder="Nom de l'analyse" />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button 
                className="btn-primary flex-1"
                onClick={() => setShowNewAnalysisModal(false)}
              >
                Lancer l'analyse
              </button>
              <button 
                className="btn-outline flex-1"
                onClick={() => setShowNewAnalysisModal(false)}
              >
                Annuler
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Analyses;
