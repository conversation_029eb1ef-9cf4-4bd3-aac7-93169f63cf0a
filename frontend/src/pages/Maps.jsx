import React from 'react';
import { MapIcon } from '@heroicons/react/24/outline';

const Maps = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-title font-bold text-night-blue">
            Cartes Interactives
          </h1>
          <p className="text-gray-600 mt-1">
            Visualisation géospatiale des données de surveillance
          </p>
        </div>
      </div>

      <div className="card text-center py-12">
        <MapIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Carte Interactive
        </h3>
        <p className="text-gray-500 mb-6">
          La carte interactive sera intégrée ici avec les données de détection.
        </p>
        <button className="btn-primary">
          Charger la carte
        </button>
      </div>
    </div>
  );
};

export default Maps;
