import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  EyeIcon,
  MapPinIcon,
  CalendarIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

const Detections = () => {
  const { user } = useAuth();
  const [detections, setDetections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: 'all',
    region: 'all',
    dateRange: '7days',
    search: '',
  });

  // Données simulées
  useEffect(() => {
    setTimeout(() => {
      setDetections([
        {
          id: 1,
          title: 'Activité minière suspecte',
          location: 'Bondoukou, Région Est',
          coordinates: [3.2, -2.8],
          date: '2024-01-15T10:30:00Z',
          status: 'active',
          confidence: 95,
          type: 'mining',
          description: 'Détection automatique d\'une nouvelle zone d\'extraction',
          images: 2,
          priority: 'high',
        },
        {
          id: 2,
          title: 'Déforestation détectée',
          location: 'Abengourou, Région Est',
          coordinates: [3.5, -3.5],
          date: '2024-01-14T15:45:00Z',
          status: 'investigating',
          confidence: 87,
          type: 'deforestation',
          description: 'Zone de déforestation de 2.3 hectares',
          images: 4,
          priority: 'medium',
        },
        {
          id: 3,
          title: 'Changement de végétation',
          location: 'Tanda, Région Est',
          coordinates: [3.1, -3.2],
          date: '2024-01-13T08:20:00Z',
          status: 'resolved',
          confidence: 72,
          type: 'vegetation',
          description: 'Modification de la couverture végétale',
          images: 1,
          priority: 'low',
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { class: 'badge-danger', text: 'Actif' },
      investigating: { class: 'badge-primary', text: 'En cours' },
      resolved: { class: 'badge-success', text: 'Résolu' },
    };
    const config = statusConfig[status] || statusConfig.active;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const getPriorityColor = (priority) => {
    const colors = {
      high: 'text-alert-red',
      medium: 'text-gold',
      low: 'text-forest-green',
    };
    return colors[priority] || colors.medium;
  };

  const filteredDetections = detections.filter(detection => {
    if (filters.status !== 'all' && detection.status !== filters.status) return false;
    if (filters.search && !detection.title.toLowerCase().includes(filters.search.toLowerCase()) &&
        !detection.location.toLowerCase().includes(filters.search.toLowerCase())) return false;
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading-spinner"></div>
        <span className="ml-2">Chargement des détections...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-title font-bold text-night-blue">
            Détections
          </h1>
          <p className="text-gray-600 mt-1">
            Surveillance automatique des activités minières
          </p>
        </div>
        <button className="btn-primary">
          <EyeIcon className="h-5 w-5 mr-2" />
          Nouvelle analyse
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher..."
              className="form-input pl-10"
              value={filters.search}
              onChange={(e) => setFilters({...filters, search: e.target.value})}
            />
          </div>

          {/* Status filter */}
          <select
            className="form-input"
            value={filters.status}
            onChange={(e) => setFilters({...filters, status: e.target.value})}
          >
            <option value="all">Tous les statuts</option>
            <option value="active">Actif</option>
            <option value="investigating">En cours</option>
            <option value="resolved">Résolu</option>
          </select>

          {/* Region filter */}
          <select
            className="form-input"
            value={filters.region}
            onChange={(e) => setFilters({...filters, region: e.target.value})}
          >
            <option value="all">Toutes les régions</option>
            <option value="est">Région Est</option>
            <option value="ouest">Région Ouest</option>
            <option value="nord">Région Nord</option>
          </select>

          {/* Date range */}
          <select
            className="form-input"
            value={filters.dateRange}
            onChange={(e) => setFilters({...filters, dateRange: e.target.value})}
          >
            <option value="7days">7 derniers jours</option>
            <option value="30days">30 derniers jours</option>
            <option value="90days">90 derniers jours</option>
          </select>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card text-center">
          <div className="text-2xl font-bold text-night-blue">{detections.length}</div>
          <div className="text-sm text-gray-600">Total détections</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-alert-red">
            {detections.filter(d => d.status === 'active').length}
          </div>
          <div className="text-sm text-gray-600">Actives</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-gold">
            {detections.filter(d => d.status === 'investigating').length}
          </div>
          <div className="text-sm text-gray-600">En cours</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-forest-green">
            {detections.filter(d => d.status === 'resolved').length}
          </div>
          <div className="text-sm text-gray-600">Résolues</div>
        </div>
      </div>

      {/* Detections List */}
      <div className="space-y-4">
        {filteredDetections.map((detection) => (
          <div key={detection.id} className="card hover:shadow-lg transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-lg font-semibold text-night-blue">
                    {detection.title}
                  </h3>
                  {getStatusBadge(detection.status)}
                  <span className={`text-sm font-medium ${getPriorityColor(detection.priority)}`}>
                    {detection.priority === 'high' ? 'Priorité élevée' :
                     detection.priority === 'medium' ? 'Priorité moyenne' : 'Priorité faible'}
                  </span>
                </div>
                
                <p className="text-gray-600 mb-3">{detection.description}</p>
                
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center">
                    <MapPinIcon className="h-4 w-4 mr-1" />
                    {detection.location}
                  </div>
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    {new Date(detection.date).toLocaleDateString('fr-FR')}
                  </div>
                  <div>
                    Confiance: {detection.confidence}%
                  </div>
                  <div>
                    {detection.images} image(s)
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2 ml-4">
                <button className="btn-outline text-sm">
                  Voir détails
                </button>
                <button className="btn-secondary text-sm">
                  Analyser
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredDetections.length === 0 && (
        <div className="text-center py-12">
          <EyeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucune détection trouvée
          </h3>
          <p className="text-gray-500">
            Aucune détection ne correspond aux critères de recherche.
          </p>
        </div>
      )}
    </div>
  );
};

export default Detections;
