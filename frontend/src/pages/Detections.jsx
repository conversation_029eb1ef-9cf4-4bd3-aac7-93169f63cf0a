import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import DetectionCard from '../components/common/DetectionCard';
import DetectionFilters from '../components/common/DetectionFilters';
import DetectionStats from '../components/common/DetectionStats';
import {
  EyeIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';

const Detections = () => {
  const { user } = useAuth();
  const [detections, setDetections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: 'all',
    region: 'all',
    dateRange: '7days',
    search: '',
    priority: 'all',
    type: 'all',
  });
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' ou 'list'

  // Données simulées enrichies
  useEffect(() => {
    setTimeout(() => {
      setDetections([
        {
          id: 1,
          title: 'Activité minière suspecte',
          location: 'Bondoukou, Région Est',
          coordinates: [3.2, -2.8],
          date: '2024-01-15T10:30:00Z',
          status: 'active',
          confidence: 95,
          type: 'mining',
          description: 'Détection automatique d\'une nouvelle zone d\'extraction aurifère avec équipements lourds identifiés',
          images: 2,
          priority: 'high',
          area: '2.3 ha',
          source: 'Satellite',
          analyst: 'Système IA',
        },
        {
          id: 2,
          title: 'Déforestation détectée',
          location: 'Abengourou, Région Est',
          coordinates: [3.5, -3.5],
          date: '2024-01-14T15:45:00Z',
          status: 'investigating',
          confidence: 87,
          type: 'deforestation',
          description: 'Zone de déforestation de 2.3 hectares liée à l\'activité minière',
          images: 4,
          priority: 'medium',
          area: '2.3 ha',
          source: 'Drone',
          analyst: 'A. Kouassi',
        },
        {
          id: 3,
          title: 'Changement de végétation',
          location: 'Tanda, Région Est',
          coordinates: [3.1, -3.2],
          date: '2024-01-13T08:20:00Z',
          status: 'resolved',
          confidence: 72,
          type: 'vegetation',
          description: 'Modification de la couverture végétale détectée par analyse multispectrale',
          images: 1,
          priority: 'low',
          area: '0.8 ha',
          source: 'Satellite',
          analyst: 'M. Diallo',
        },
        {
          id: 4,
          title: 'Nouvelle excavation',
          location: 'Koun-Fao, Région Est',
          coordinates: [3.4, -2.9],
          date: '2024-01-12T14:20:00Z',
          status: 'active',
          confidence: 92,
          type: 'mining',
          description: 'Excavation récente détectée avec présence d\'équipements miniers',
          images: 3,
          priority: 'high',
          area: '1.7 ha',
          source: 'Drone',
          analyst: 'Système IA',
        },
        {
          id: 5,
          title: 'Pollution de cours d\'eau',
          location: 'Agnibilékrou, Région Est',
          coordinates: [3.2, -3.1],
          date: '2024-01-11T11:45:00Z',
          status: 'investigating',
          confidence: 85,
          type: 'pollution',
          description: 'Changement de couleur d\'un cours d\'eau près d\'une zone minière',
          images: 2,
          priority: 'high',
          area: '0.5 km',
          source: 'Satellite',
          analyst: 'K. Yao',
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  // Gestionnaires d'événements
  const handleViewDetails = (detection) => {
    console.log('Voir détails:', detection);
    // TODO: Implémenter la navigation vers la page de détails
  };

  const handleAnalyze = (detection) => {
    console.log('Analyser:', detection);
    // TODO: Implémenter l'analyse de la détection
  };

  // Filtrage et tri
  const filteredDetections = detections.filter(detection => {
    if (filters.status !== 'all' && detection.status !== filters.status) return false;
    if (filters.priority !== 'all' && detection.priority !== filters.priority) return false;
    if (filters.type !== 'all' && detection.type !== filters.type) return false;
    if (filters.search && !detection.title.toLowerCase().includes(filters.search.toLowerCase()) &&
        !detection.location.toLowerCase().includes(filters.search.toLowerCase())) return false;
    return true;
  }).sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    if (sortBy === 'date') {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Statistiques
  const stats = {
    total: detections.length,
    active: detections.filter(d => d.status === 'active').length,
    investigating: detections.filter(d => d.status === 'investigating').length,
    resolved: detections.filter(d => d.status === 'resolved').length,
    highPriority: detections.filter(d => d.priority === 'high').length,
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading-spinner"></div>
        <span className="ml-2 text-gray-600">Chargement des détections...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header avec statistiques */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-title font-bold text-night-blue">
              Détections d'Orpaillage
            </h1>
            <p className="text-gray-600 mt-1">
              Gestion et validation des détections automatiques
            </p>
          </div>
          <div className="flex space-x-3 mt-4 lg:mt-0">
            <button className="btn-outline">
              <FunnelIcon className="h-5 w-5 mr-2" />
              Filtres
            </button>
            <button className="btn-primary">
              <EyeIcon className="h-5 w-5 mr-2" />
              Nouvelle analyse
            </button>
          </div>
        </div>

        {/* Statistiques en ligne */}
        <DetectionStats stats={stats} />
      </div>

      {/* Filtres et contrôles */}
      <DetectionFilters
        filters={filters}
        onFiltersChange={setFilters}
        sortBy={sortBy}
        setSortBy={setSortBy}
        sortOrder={sortOrder}
        setSortOrder={setSortOrder}
      />

      {/* Liste des détections */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-night-blue">
              Détections ({filteredDetections.length})
            </h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-gold text-night-blue' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-gold text-night-blue' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {filteredDetections.length === 0 ? (
            <div className="text-center py-12">
              <EyeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucune détection trouvée
              </h3>
              <p className="text-gray-500">
                Aucune détection ne correspond aux critères de recherche.
              </p>
            </div>
          ) : (
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6' : 'space-y-4'}>
              {filteredDetections.map((detection) => (
                <DetectionCard
                  key={detection.id}
                  detection={detection}
                  viewMode={viewMode}
                  onViewDetails={handleViewDetails}
                  onAnalyze={handleAnalyze}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Detections;
