import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

// Configuration axios
const API_BASE_URL = 'http://localhost:8000/api/v1';

axios.defaults.baseURL = API_BASE_URL;
axios.defaults.headers.common['Content-Type'] = 'application/json';

// Intercepteur pour ajouter le token aux requêtes
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs d'authentification
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post('/auth/token/refresh/', {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);
          
          // Retry la requête originale avec le nouveau token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return axios(originalRequest);
        }
      } catch (refreshError) {
        // Le refresh token est invalide, déconnecter l'utilisateur
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const userData = localStorage.getItem('user');

      if (token && userData) {
        setUser(JSON.parse(userData));
        setIsAuthenticated(true);
        
        // Vérifier si le token est encore valide
        try {
          await axios.get('/auth/profile/');
        } catch (error) {
          // Token invalide, déconnecter
          logout();
        }
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du statut d\'authentification:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const response = await axios.post('/auth/login/', {
        email,
        password,
      });

      const { access, refresh, user: userData } = response.data;

      // Stocker les tokens et les données utilisateur
      localStorage.setItem('access_token', access);
      localStorage.setItem('refresh_token', refresh);
      localStorage.setItem('user', JSON.stringify(userData));

      setUser(userData);
      setIsAuthenticated(true);

      return { success: true, user: userData };
    } catch (error) {
      console.error('Erreur de connexion:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
      };
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    setUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (userData) => {
    setUser(userData);
    localStorage.setItem('user', JSON.stringify(userData));
  };

  // Fonction pour vérifier les permissions
  const hasPermission = (permission) => {
    if (!user || !user.authorities) return false;
    
    // Mapping des permissions selon les autorités
    const permissionMap = {
      'can_view_stats': ['Administrateur', 'Responsable Régional', 'Agent Analyste', 'Agent Technique'],
      'can_manage_investigations': ['Administrateur', 'Responsable Régional', 'Agent Analyste'],
      'can_launch_analysis': ['Administrateur', 'Responsable Régional', 'Agent Analyste', 'Agent Technique'],
      'can_view_logs': ['Administrateur', 'Agent Technique'],
      'is_admin': ['Administrateur'],
      'can_manage_users': ['Administrateur'],
      'can_view_reports': ['Administrateur', 'Responsable Régional', 'Agent Analyste'],
      'can_create_reports': ['Responsable Régional', 'Agent Terrain'],
    };

    const allowedAuthorities = permissionMap[permission] || [];
    return user.authorities.some(auth => allowedAuthorities.includes(auth));
  };

  // Fonction pour obtenir le rôle principal
  const getPrimaryRole = () => {
    if (!user || !user.primary_authority) return null;
    return user.primary_authority;
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    updateUser,
    hasPermission,
    getPrimaryRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
